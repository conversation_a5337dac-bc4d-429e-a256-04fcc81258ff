"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface HeroEntry {
  id: number;
  videoUrl: string;
  titles: string[];
  subtitles: string[];
  images: string[];
}

const demoHeroes: HeroEntry[] = [
  {
    id: 1,
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    titles: ['Welcome to Our Site'],
    subtitles: ['Discover amazing content that engages your audience effectively.'],
    images: [
      '/images/random.jpeg',
      '/images/random.jpeg',
      '/images/random.jpeg',
      '/images/random.jpeg',
      '/images/random.jpeg',
    ],
  },
  {
    id: 2,
    videoUrl: 'https://player.vimeo.com/video/76979871',
    titles: ['Our Latest Update'],
    subtitles: ['Stay tuned for more exciting news and features coming your way soon!'],
    images: [
      '/images/random.jpeg',
      '/images/random.jpeg',
    ],
  },
];

const HeroListPage: React.FC = () => {
  const visibleImages = 2;
  const handleDelete = (id: number) => {
    // TODO: implement delete logic
    console.log('Delete hero with id:', id);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Content</h1>
        <Link href="/hero/create">
          <button className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
            + Add Content
          </button>
        </Link>
      </div>

      {/* Table Container */}
      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-white border-b">
            <tr>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-700 uppercase">
                Video
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-700 uppercase">
                Images
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-700 uppercase">
                Title
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-700 uppercase">
                Description
              </th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody>
            {demoHeroes.map((hero) => {
              const firstTitle = hero.titles[0] || '';
              const firstSubtitle = hero.subtitles[0] || '';
              const extraCount = hero.images.length - visibleImages;

              return (
                <tr key={hero.id} className="border-t">
                  {/* Video Cell */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link href={hero.videoUrl} target="_blank" className="text-blue-600 hover:underline">
                      View
                    </Link>
                  </td>

                  {/* Images Cell */}
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      {hero.images.slice(0, visibleImages).map((src, idx) => (
                        <Image
                          key={idx}
                          src={src}
                          alt={`content-img-${hero.id}-${idx}`}
                          width={64}
                          height={64}
                          className="object-cover rounded"
                        />
                      ))}
                      {extraCount > 0 && (
                        <div className="h-16 w-16 flex items-center justify-center bg-gray-100 rounded text-gray-600 text-sm">
                          +{extraCount}
                        </div>
                      )}
                    </div>
                  </td>

                  {/* Title Cell */}
                  <td className="px-6 py-4 text-sm text-gray-700 whitespace-normal">
                    {firstTitle}
                  </td>

                  {/* Description Cell */}
                  <td className="px-6 py-4 text-sm text-gray-700 whitespace-normal truncate">
                    {firstSubtitle}
                  </td>

                  {/* Actions Cell */}
                  <td className="px-6 py-4 text-right whitespace-nowrap text-sm font-medium">
                    <Link href={`/hero/edit/${hero.id}`}> 
                      <button className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2">
                        Edit
                      </button>
                    </Link>
                    <button
                      onClick={() => handleDelete(hero.id)}
                      className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HeroListPage;
