import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

// Demo data for now
interface HeroEntry {
  id: number;
  titles: string[];
  subtitles: string[];
  images: string[];
}

const demoHeroes: HeroEntry[] = [
  {
    id: 1,
    titles: ['Welcome to Our Site'],
    subtitles: ['Discover amazing content'],
    images: [
      '/images/random.jpeg',
      '/images/random.jpeg',
      '/images/random.jpeg',
    ],
  },
  {
    id: 2,
    titles: ['Our Latest Update'],
    subtitles: ['Stay tuned for more'],
    images: [
      '/images/random.jpeg',
      '/images/random.jpeg',
    ],
  },
];

const HeroListPage: React.FC = () => {
  const handleDelete = (id: number) => {
    // TODO: replace with actual delete logic
    console.log('Delete hero with id:', id);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-6">Hero Entries</h1>

      <div className="overflow-x-auto bg-white rounded shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Titles
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Subtitles
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Images
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {demoHeroes.map((hero) => (
              <tr key={hero.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {hero.id}
                </td>
                <td className="px-6 py-4 whitespace-normal text-sm text-gray-700">
                  {hero.titles.join(', ')}
                </td>
                <td className="px-6 py-4 whitespace-normal text-sm text-gray-700">
                  {hero.subtitles.join(', ')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex space-x-2">
                    {hero.images.map((src, idx) => (
                      <Image
                        key={idx}
                        src={src}
                        alt={`hero-img-${hero.id}-${idx}`}
                        width={40}
                        height={40}
                        className="h-10 w-10 object-cover rounded"
                      />
                    ))}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <Link href={`/hero/edit/${hero.id}`}>
                    <button className="px-3 py-1 bg-blue-600 text-white rounded mr-2">
                      Edit
                    </button>
                  </Link>
                  <button
                    onClick={() => handleDelete(hero.id)}
                    className="px-3 py-1 bg-red-600 text-white rounded"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HeroListPage;
